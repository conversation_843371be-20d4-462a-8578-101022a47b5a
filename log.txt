fighting onEventFocusOn
index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:342 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:342 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:342 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:342 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:342 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: false
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0.7260000000000004
index.29264.js:242730 e._isAlive: false
index.29264.js:242731 e.isAlive(): false
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

vendor.61f008fd3a658237ec95.js:62 resData {env: 'fcm_pro', result: {…}, code: 200, message: '不需要进行防沉迷，持续心跳', sign: 'e14e524f137791d13e472fac5ee83723'}
vendor.61f008fd3a658237ec95.js:62 transform {code: 12200, message: '不需要进行防沉迷，持续心跳', data: {…}}
index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====

index.29264.js:242718 ===== 🌊 S4水元素领主状态监控 ID:341 =====
index.29264.js:242719 实体存在: true
index.29264.js:242720 阵营: 101
index.29264.js:242721 info对象: exists
index.29264.js:242724 名称: S4水元素领主
index.29264.js:242725 e.info.alive: true
index.29264.js:242726 deadCount: 0
index.29264.js:242727 refreshIntervalTime: 0
index.29264.js:242730 e._isAlive: true
index.29264.js:242731 e.isAlive(): true
index.29264.js:242732 isDestoryed: false
index.29264.js:242733 ===== S4水元素领主监控结束 =====