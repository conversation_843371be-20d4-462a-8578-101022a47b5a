===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 286.5719999999682s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 287.6279999999687s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 287.6279999999687s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 288.6839999999692s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 288.6839999999692s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 289.7399999999697s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 289.7399999999697s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 290.7959999999702s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 290.7959999999702s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 291.8519999999707s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 291.8519999999707s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 292.9079999999712s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 292.9079999999712s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 293.9639999999717s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:1763 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10442
index.29264.js:242724 e.info.alive: false
index.29264.js:242725 e._isAlive: false
index.29264.js:242726 e.isAlive(): false
index.29264.js:242727 战斗时间: 293.9639999999717s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0.8910000000000006
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (-3405, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 119.1s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 295.0199999999722s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 296.0759999999727s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 297.1319999999732s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 298.18799999997367s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 299.24399999997416s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 300.29999999997466s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 301.35599999997515s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 302.41199999997565s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 303.46799999997614s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:31687 =========skill onEnterFighting=====================> S4白骨精
index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 304.52399999997664s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 305.57999999997713s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 306.6359999999776s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 307.6919999999781s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 308.7479999999786s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:31687 =========skill onEnterFighting=====================> S4白骨精
index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 309.8039999999791s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 310.8599999999796s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗时间: 311.9159999999801s
index.29264.js:242728 refreshTime1: 120s
index.29264.js:242729 refreshTime2: 120s
index.29264.js:242730 refreshIntervalTime: 0
index.29264.js:242731 deadCount: 0
index.29264.js:242732 level: 10
index.29264.js:242733 位置: (3435, 1233)
index.29264.js:242741 当前应用刷新时间: 120s
index.29264.js:242742 计算方式1 (秒*1000-毫秒): 120.0s
index.29264.js:242743 计算方式2 (秒-毫秒/1000): 120.0s
index.29264.js:242744 计算方式3 (秒-秒): 120.0s
index.29264.js:242745 ===== 🌊 水元素领主调试结束 =====

index.29264.js:242721 ===== 🌊 S4水元素领主调试 ID:309 =====
index.29264.js:242722 名称: S4水元素领主
index.29264.js:242723 配置ID: 10443
index.29264.js:242724 e.info.alive: true
index.29264.js:242725 e._isAlive: true
index.29264.js:242726 e.isAlive(): true
index.29264.js:242727 战斗