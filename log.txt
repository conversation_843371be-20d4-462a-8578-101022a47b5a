===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10455, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1385, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: false
index.29264.js:141211 当前累积时间: 52.041000000001276ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 52.07400000000128ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 7.925999999998723ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141246 ===== 野区生物监控结束 =====

index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10458, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2530, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10458仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10452, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2270, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10452仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10460, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1585, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10460仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10459, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1615, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10459仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10453, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-4155, 1033), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10453仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10451, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (4185, 1033), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10451仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10443, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (3435, 1233), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10443仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10441, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (15, -17), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10441仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10433, 配置ID: N/A
index.29264.js:141208 野怪类型: 8, 阵营: 101
index.29264.js:141209 位置: (-35, -1617), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 180ms, 剩余时间: 179.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10433仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10442, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (-3405, 1233), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10442仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10431, 配置ID: N/A
index.29264.js:141208 野怪类型: 8, 阵营: 101
index.29264.js:141209 位置: (15, 1703), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 180ms, 剩余时间: 179.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10431仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 50003, 配置ID: N/A
index.29264.js:141208 野怪类型: 14, 阵营: 101
index.29264.js:141209 位置: (1655, -907), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪50003仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 50002, 配置ID: N/A
index.29264.js:141208 野怪类型: 14, 阵营: 101
index.29264.js:141209 位置: (-1625, -907), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1010000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪50002仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10456, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-2500, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10456仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10461, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1775.76, -1717.06), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10461仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10462, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1797.57, -1726.4), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10462仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10454, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-2240, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10454仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10457, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1415, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10457仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10455, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1385, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: false
index.29264.js:141211 当前累积时间: 52.07400000000128ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 52.10700000000128ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 7.892999999998722ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141246 ===== 野区生物监控结束 =====

index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10458, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2530, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10458仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10452, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2270, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10452仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10460, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1585, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10460仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10459, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1615, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10459仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10453, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-4155, 1033), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10453仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10451, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (4185, 1033), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10451仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10443, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (3435, 1233), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10443仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10441, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (15, -17), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10441仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10433, 配置ID: N/A
index.29264.js:141208 野怪类型: 8, 阵营: 101
index.29264.js:141209 位置: (-35, -1617), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 180ms, 剩余时间: 179.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10433仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
 野怪ID: 10442, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (-3405, 1233), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10442仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10431, 配置ID: N/A
 野怪类型: 8, 阵营: 101
 位置: (15, 1703), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 180ms, 剩余时间: 179.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10431仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 50003, 配置ID: N/A
 野怪类型: 14, 阵营: 101
 位置: (1655, -907), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪50003仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 50002, 配置ID: N/A
 野怪类型: 14, 阵营: 101
 位置: (-1625, -907), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1340000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪50002仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10456, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (-2500, 13), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10456仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10461, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (-1775.76, -1717.06), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10461仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10462, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (1797.57, -1726.4), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10462仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10454, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-2240, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10454仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10457, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1415, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10457仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10455, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1385, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: false
index.29264.js:141211 当前累积时间: 52.10700000000128ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 52.14000000000128ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 7.8599999999987205ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141246 ===== 野区生物监控结束 =====

index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10458, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2530, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10458仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10452, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2270, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10452仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10460, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1585, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10460仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10459, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (1615, 13), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10459仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10453, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (-4155, 1033), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10453仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10451, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (4185, 1033), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10451仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10443, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (3435, 1233), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10443仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10441, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (15, -17), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10441仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10433, 配置ID: N/A
 野怪类型: 8, 阵营: 101
 位置: (-35, -1617), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 180ms, 剩余时间: 179.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10433仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10442, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (-3405, 1233), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10442仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10431, 配置ID: N/A
 野怪类型: 8, 阵营: 101
 位置: (15, 1703), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 180ms, 剩余时间: 179.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10431仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 50003, 配置ID: N/A
 野怪类型: 14, 阵营: 101
 位置: (1655, -907), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪50003仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 50002, 配置ID: N/A
 野怪类型: 14, 阵营: 101
index.29264.js:141209 位置: (-1625, -907), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.1670000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪50002仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10456, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-2500, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10456仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10461, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1775.76, -1717.06), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10461仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10462, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1797.57, -1726.4), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10462仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10454, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-2240, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10454仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10457, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1415, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10457仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10455, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1385, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: false
index.29264.js:141211 当前累积时间: 52.14000000000128ms
index.29264.js:141216 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 52.17300000000128ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 7.826999999998719ms
 处理野区生物刷新逻辑
 ===== 野区生物监控结束 =====

 ===== 野区生物刷新监控 =====
 野怪ID: 10458, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (2530, 13), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10458仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10452, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (2270, 1513), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10452仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10460, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (-1585, 13), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10460仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10459, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (1615, 13), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10459仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10453, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (-4155, 1033), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10453仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10451, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (4185, 1033), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10451仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10443, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (3435, 1233), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10443仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10441, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (15, -17), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10441仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10433, 配置ID: N/A
 野怪类型: 8, 阵营: 101
 位置: (-35, -1617), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 180ms, 剩余时间: 179.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10433仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10442, 配置ID: N/A
 野怪类型: 7, 阵营: 101
 位置: (-3405, 1233), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 120ms, 剩余时间: 119.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10442仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10431, 配置ID: N/A
 野怪类型: 8, 阵营: 101
 位置: (15, 1703), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 180ms, 剩余时间: 179.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪10431仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 50003, 配置ID: N/A
 野怪类型: 14, 阵营: 101
 位置: (1655, -907), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪50003仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 50002, 配置ID: N/A
 野怪类型: 14, 阵营: 101
 位置: (-1625, -907), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2000000001802s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
 ⏸️ 野怪50002仍存活，重置计时器
 ===== 野区生物刷新监控 =====
 野怪ID: 10456, 配置ID: N/A
 野怪类型: 6, 阵营: 101
 位置: (-2500, 13), 组别: N/A
 刷新类型: 2, 是否存活: true
 当前累积时间: 0ms
 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
 更新后累积时间: 0.033ms
 当前刷新周期: 60ms, 剩余时间: 59.967ms
 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10456仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10461, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1775.76, -1717.06), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10461仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10462, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1797.57, -1726.4), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10462仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10454, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-2240, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10454仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10457, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1415, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10457仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10455, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1385, -397), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: false
index.29264.js:141211 当前累积时间: 52.17300000000128ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 52.20600000000128ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 7.793999999998718ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141246 ===== 野区生物监控结束 =====

index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10458, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2530, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10458仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10452, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (2270, 1513), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10452仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10460, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-1585, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10460仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10459, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (1615, 13), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10459仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10453, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (-4155, 1033), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10453仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10451, 配置ID: N/A
index.29264.js:141208 野怪类型: 6, 阵营: 101
index.29264.js:141209 位置: (4185, 1033), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10451仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10443, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (3435, 1233), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10443仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10441, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (15, -17), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10441仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10433, 配置ID: N/A
index.29264.js:141208 野怪类型: 8, 阵营: 101
index.29264.js:141209 位置: (-35, -1617), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 180ms, 剩余时间: 179.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10433仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10442, 配置ID: N/A
index.29264.js:141208 野怪类型: 7, 阵营: 101
index.29264.js:141209 位置: (-3405, 1233), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 120ms, 剩余时间: 119.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10442仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 10431, 配置ID: N/A
index.29264.js:141208 野怪类型: 8, 阵营: 101
index.29264.js:141209 位置: (15, 1703), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 180ms, 剩余时间: 179.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪10431仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 50003, 配置ID: N/A
index.29264.js:141208 野怪类型: 14, 阵营: 101
index.29264.js:141209 位置: (1655, -907), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪50003仍存活，重置计时器
index.29264.js:141206 ===== 野区生物刷新监控 =====
index.29264.js:141207 野怪ID: 50002, 配置ID: N/A
index.29264.js:141208 野怪类型: 14, 阵营: 101
index.29264.js:141209 位置: (-1625, -907), 组别: N/A
index.29264.js:141210 刷新类型: 2, 是否存活: true
index.29264.js:141211 当前累积时间: 0ms
index.29264.js:141216 战斗总时间: 739.2330000001803s, 逻辑时间增量: 0.033ms
index.29264.js:141217 更新后累积时间: 0.033ms
index.29264.js:141221 当前刷新周期: 60ms, 剩余时间: 59.967ms
index.29264.js:141224 处理野区生物刷新逻辑
index.29264.js:141226 ⏸️ 野怪50002仍存活，重置计时器